import { useEffect, useMemo, useCallback } from 'react';
import { fieldsExcludeMetaFields } from 'constants/shared';
import { Product } from 'types/product';
import {
  useGetProductCriticalQuery,
  useLazyGetProductBasicInfoQuery,
  useLazyGetProductExtendedInfoQuery,
  useLazyGetProductPoliciesQuery,
  useLazyGetProductAssetsQuery,
  useLazyGetProductFacetsQuery,
  useLazyGetProductCustomizationsQuery,
} from 'redux/app/products/optimizedProductApiSlice';

interface UseOptimizedProductOptions {
  productId: string;
  loadSecondaryData?: boolean;
  throttleDelay?: number; // Delay between secondary requests in ms
}

interface UseOptimizedProductReturn {
  product: Product | undefined;
  isLoading: boolean;
  isError: boolean;
  isSecondaryDataLoading: boolean;
  loadingProgress: {
    critical: boolean;
    basicInfo: boolean;
    extendedInfo: boolean;
    policies: boolean;
    assets: boolean;
    facets: boolean;
    customizations: boolean;
  };
  error: any;
  refetch: () => void;
}

/**
 * Optimized hook for loading product data with memory efficiency
 * Loads data in chunks with throttling to prevent server memory overload
 */
export function useOptimizedProduct({
  productId,
  loadSecondaryData = true,
  throttleDelay = 800, // Conservative delay for admin UI
}: UseOptimizedProductOptions): UseOptimizedProductReturn {
  
  // Load critical product data first (smallest payload)
  const {
    data: productCritical,
    isLoading: criticalLoading,
    isError: criticalError,
    error: criticalErrorData,
    refetch: refetchCritical,
  } = useGetProductCriticalQuery({
    id: productId,
    filter: {
      fields: fieldsExcludeMetaFields,
    },
  });

  // Lazy queries for granular secondary data loading
  const [triggerBasicInfo, { data: basicInfo, isLoading: basicLoading }] = 
    useLazyGetProductBasicInfoQuery();
  
  const [triggerExtendedInfo, { data: extendedInfo, isLoading: extendedLoading }] = 
    useLazyGetProductExtendedInfoQuery();
  
  const [triggerPolicies, { data: policies, isLoading: policiesLoading }] = 
    useLazyGetProductPoliciesQuery();
  
  const [triggerAssets, { data: assets, isLoading: assetsLoading }] = 
    useLazyGetProductAssetsQuery();
  
  const [triggerFacets, { data: facets, isLoading: facetsLoading }] = 
    useLazyGetProductFacetsQuery();
  
  const [triggerCustomizations, { data: customizations, isLoading: customizationsLoading }] = 
    useLazyGetProductCustomizationsQuery();

  // Throttled loading function to prevent server overload
  const loadDataWithThrottle = useCallback(async () => {
    if (!productCritical?.id || !loadSecondaryData) return;

    // Load basic info first (smallest secondary payload)
    await new Promise(resolve => setTimeout(resolve, throttleDelay));
    triggerBasicInfo({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });

    // Load extended info after delay
    await new Promise(resolve => setTimeout(resolve, throttleDelay));
    triggerExtendedInfo({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });

    // Load policies after delay
    await new Promise(resolve => setTimeout(resolve, throttleDelay));
    triggerPolicies({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });

    // Load assets after delay (can be large)
    await new Promise(resolve => setTimeout(resolve, throttleDelay * 1.5));
    triggerAssets({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });

    // Load facets after delay
    await new Promise(resolve => setTimeout(resolve, throttleDelay));
    triggerFacets({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });

    // Load customizations last
    await new Promise(resolve => setTimeout(resolve, throttleDelay));
    triggerCustomizations({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
      },
    });
  }, [
    productCritical?.id,
    loadSecondaryData,
    throttleDelay,
    triggerBasicInfo,
    triggerExtendedInfo,
    triggerPolicies,
    triggerAssets,
    triggerFacets,
    triggerCustomizations,
  ]);

  // Trigger throttled loading after critical data is available
  useEffect(() => {
    loadDataWithThrottle();
  }, [loadDataWithThrottle]);

  // Combine all data progressively
  const combinedProduct = useMemo(() => {
    if (!productCritical) return undefined;

    return {
      ...productCritical,
      // Merge basic info
      ...(basicInfo && {
        productSpecifications: basicInfo.productSpecifications || productCritical.productSpecifications,
        productDetail: basicInfo.productDetail || productCritical.productDetail,
        productBoxContents: basicInfo.productBoxContents || productCritical.productBoxContents,
      }),
      // Merge extended info
      ...(extendedInfo && {
        productMoreInfo: extendedInfo.productMoreInfo || productCritical.productMoreInfo,
        productUniqueness: extendedInfo.productUniqueness || productCritical.productUniqueness,
        productSuitability: extendedInfo.productSuitability || productCritical.productSuitability,
      }),
      // Merge policies
      ...(policies && {
        productReturnPolicy: policies.productReturnPolicy || productCritical.productReturnPolicy,
        productTermsAndCondition: policies.productTermsAndCondition || productCritical.productTermsAndCondition,
        productDisclaimer: policies.productDisclaimer || productCritical.productDisclaimer,
        productPersonalWork: policies.productPersonalWork || productCritical.productPersonalWork,
      }),
      // Merge assets
      ...(assets && {
        productAssets: assets.productAssets || productCritical.productAssets,
      }),
      // Merge facets
      ...(facets && {
        productFacetValues: facets.productFacetValues || productCritical.productFacetValues,
      }),
      // Merge customizations
      ...(customizations && {
        productCustomizationFields: customizations.productCustomizationFields || productCritical.productCustomizationFields,
      }),
    } as Product;
  }, [
    productCritical,
    basicInfo,
    extendedInfo,
    policies,
    assets,
    facets,
    customizations,
  ]);

  const isSecondaryDataLoading = 
    basicLoading || 
    extendedLoading || 
    policiesLoading || 
    assetsLoading ||
    facetsLoading ||
    customizationsLoading;

  const refetch = useCallback(() => {
    refetchCritical();
    // Trigger secondary data reload after critical data is refreshed
    if (loadSecondaryData) {
      setTimeout(() => {
        loadDataWithThrottle();
      }, 100);
    }
  }, [refetchCritical, loadSecondaryData, loadDataWithThrottle]);

  return {
    product: combinedProduct,
    isLoading: criticalLoading,
    isError: criticalError,
    isSecondaryDataLoading,
    loadingProgress: {
      critical: !criticalLoading,
      basicInfo: !!basicInfo,
      extendedInfo: !!extendedInfo,
      policies: !!policies,
      assets: !!assets,
      facets: !!facets,
      customizations: !!customizations,
    },
    error: criticalErrorData,
    refetch,
  };
}
