'use client';
import Loader from 'components/Loader';
import { useParams } from 'next/navigation';
import { useMemo } from 'react';
import { useOptimizedProduct } from 'hooks/useOptimizedProduct';
import { useGetSellersQuery } from 'redux/app/seller/sellerApiSlice';
import { ProductDto } from 'types/product-dto';
import EditProduct from 'views/products/Form/EditForm';

const EditProductPage = () => {
  const params = useParams();

  // Use optimized hook for progressive data loading
  const { product, isLoading, refetch } = useOptimizedProduct({
    productId: params.id as string,
    loadSecondaryData: true,
    throttleDelay: 600 // Faster for edit form
  });
  const { data: sellers, isLoading: sellerLoading } = useGetSellersQuery(
    {
      limit: 1,
      where: { id: product?.sellerId },
      include: [
        {
          relation: 'userTenant',
          required: true,
          scope: {
            include: [
              {
                relation: 'user',
                required: true
              }
            ]
          }
        }
      ]
    },
    { skip: !product?.sellerId, refetchOnMountOrArgChange: true }
  );
  const initialValue: ProductDto | undefined = useMemo(() => {
    if (!product) return undefined;
    return {
      name: product.name,
      description: product.description,
      enabled: product.enabled,
      assets: product.productAssets?.map((item) => item.assetId),
      options: [],
      facets: product.productFacetValues?.map((item) => item.facetValueId),
      boxContents: product.productBoxContents?.map((item) => ({ itemName: item.itemName, quantity: item.quantity })),
      specifications: product.productSpecifications?.map((item) => ({ name: item.name, value: item.value })),
      sellerId: product.sellerId,
      isGiftWrapAvailable: product.isGiftWrapAvailable,
      isGiftWrapCharge: product.isGiftWrapCharge,
      featuredAssetId: product.featuredAssetId,
      collectionId: product.collection?.id ?? '',
      turnAroundTime: product.turnAroundTime ?? 0,
      variants: [],

      customizations: product.productCustomizationFields?.map((item) => ({
        name: item.name,
        label: item.label,
        placeholder: item.placeholder,
        fieldType: item.fieldType,
        isRequired: item.isRequired,
        options: item.productCustomizationOptions ?? []
      })),
      taxCategoryId: product.taxCategoryId ?? undefined,
      suitability: product.productSuitability?.suitableFor ? { suitableFor: product.productSuitability.suitableFor } : undefined,
      uniqueness: product.productUniqueness?.uniqueness ? { uniqueness: product.productUniqueness.uniqueness } : undefined,
      personalWork: product.productPersonalWork?.workLevel ? { workLevel: product.productPersonalWork.workLevel } : undefined,
      terms: product?.productTermsAndCondition?.terms ? { terms: product.productTermsAndCondition?.terms } : undefined,
      returnPolicy: product?.productReturnPolicy?.returnPolicy ? { returnPolicy: product.productReturnPolicy?.returnPolicy } : undefined,
      disclaimer: product?.productDisclaimer?.disclaimer ? { disclaimer: product.productDisclaimer?.disclaimer } : undefined,
      details: product?.productDetail?.details ? { details: product.productDetail?.details } : undefined,
      averageWeight: product.averageWeight ?? 0,
      height: product.height ?? 0,
      breadth: product.breadth ?? 0,
      length: product.length ?? 0
    };
  }, [product]);

  const assets = useMemo(() => {
    if (!product) return [];
    return product.productAssets?.map((item) => ({
      id: item.assetId,
      preview: item.asset?.previewUrl,
      mimeType: item.asset?.mimeType ?? ''
    }));
  }, [product]);

  if (isLoading || product === undefined || initialValue === undefined || sellerLoading || !sellers?.length || !assets) return <Loader />;
  return (
    <EditProduct
      initialValue={initialValue}
      initialCollection={product.collection}
      initialTaxCategory={product.taxCategory ?? []}
      initialSeller={sellers[0]}
      initialFacets={product.productFacetValues ?? []}
      initialAssets={assets}
      productId={params.id as string}
      refetch={refetch}
      productVariants={product.productVariants ?? []}
    />
  );
};

export default EditProductPage;
