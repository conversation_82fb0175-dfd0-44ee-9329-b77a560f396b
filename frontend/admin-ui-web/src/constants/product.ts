import { ProductDto } from 'types/product-dto';
import { fieldsExcludeMetaFields } from './shared';

export const initialProductValue: ProductDto = {
  name: '',
  enabled: true,
  assets: [],
  options: [],
  facets: [],
  boxContents: [],
  details: undefined,
  specifications: [],
  returnPolicy: undefined,
  terms: undefined,
  sellerId: '',
  featuredAssetId: '',
  collectionId: '',
  description: '',
  variants: [],
  customizations: [],
  disclaimer: undefined,
  isGiftWrapAvailable: false,
  isGiftWrapCharge: null,
  uniqueness: undefined,
  suitability: undefined,
  personalWork: undefined
};

const metaFields = ['deleted', 'deletedOn', 'deletedBy', 'createdBy', 'createdOn', 'modifiedOn', 'modifiedBy'];

// Critical relations needed for immediate display
const criticalIncludes = ['collection', 'productVariants', 'featuredAsset'].map((relation) => ({
  relation,
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    )
  }
}));

// Basic info relations (specifications, details, box contents)
const basicInfoIncludes = ['productSpecifications', 'productDetail', 'productBoxContents'].map((relation) => ({
  relation,
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    )
  }
}));

// Extended info relations (more info, uniqueness, suitability)
const extendedInfoIncludes = ['productMoreInfo', 'productUniqueness', 'productSuitability'].map((relation) => ({
  relation,
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    )
  }
}));

// Policy relations (return policy, terms, disclaimer, personal work)
const policyIncludes = ['productReturnPolicy', 'productTermsAndCondition', 'productDisclaimer', 'productPersonalWork'].map((relation) => ({
  relation,
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    )
  }
}));

// Legacy - all direct includes combined (for backward compatibility)
const directIncludes = [...criticalIncludes, ...basicInfoIncludes, ...extendedInfoIncludes, ...policyIncludes];

// Optimized include sets for progressive loading
export const PRODUCT_CRITICAL_INCLUDES = [
  ...criticalIncludes,
  {
    relation: 'productVariants',
    scope: {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ],
      where: {
        deleted: false
      }
    }
  }
];

export const PRODUCT_BASIC_INFO_INCLUDES = basicInfoIncludes;
export const PRODUCT_EXTENDED_INFO_INCLUDES = extendedInfoIncludes;
export const PRODUCT_POLICY_INCLUDES = policyIncludes;

// Complex relations that should be loaded separately
export const PRODUCT_ASSETS_INCLUDE = {
  relation: 'productAssets',
  scope: {
    fields: metaFields.reduce(
      (acc, field) => {
        acc[field] = false;
        return acc;
      },
      {} as Record<string, boolean>
    ),
    order: ['position ASC'],
    include: [
      {
        relation: 'asset',
        scope: {
          fields: metaFields.reduce(
            (acc, field) => {
              acc[field] = false;
              return acc;
            },
            {} as Record<string, boolean>
          )
        }
      }
    ]
  }
};

export const PRODUCT_FACETS_INCLUDE = {
  relation: 'productFacetValues',
  scope: {
    include: [
      {
        relation: 'facetValue',
        scope: {
          include: [
            {
              relation: 'facet',
              scope: {
                fields: metaFields.reduce(
                  (acc, field) => {
                    acc[field] = false;
                    return acc;
                  },
                  {} as Record<string, boolean>
                )
              }
            }
          ],
          fields: metaFields.reduce(
            (acc, field) => {
              acc[field] = false;
              return acc;
            },
            {} as Record<string, boolean>
          )
        }
      }
    ]
  }
};

export const PRODUCT_CUSTOMIZATIONS_INCLUDE = {
  relation: 'productCustomizationFields',
  scope: {
    where: {
      productVariantId: null
    },
    fields: fieldsExcludeMetaFields,
    include: [
      {
        relation: 'productCustomizationOptions',
        scope: {
          fields: fieldsExcludeMetaFields
        }
      }
    ]
  }
};

// Legacy - full includes for backward compatibility
export const customIncludes = [
  ...directIncludes,
  PRODUCT_ASSETS_INCLUDE,
  PRODUCT_FACETS_INCLUDE,
  {
    relation: 'productVariants',
    scope: {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: fieldsExcludeMetaFields
          }
        }
      ],
      where: {
        deleted: false
      }
    }
  },
  PRODUCT_CUSTOMIZATIONS_INCLUDE
];
