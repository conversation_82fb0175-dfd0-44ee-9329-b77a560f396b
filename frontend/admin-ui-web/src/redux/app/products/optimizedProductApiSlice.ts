import { apiSlice } from '../../apiSlice';
import { buildFilterParams } from 'utils/buildFilterParams';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { Product } from 'types/product';
import { IFilter } from '../types/filter';
import {
  PRODUCT_CRITICAL_INCLUDES,
  PRODUCT_BASIC_INFO_INCLUDES,
  PRODUCT_EXTENDED_INFO_INCLUDES,
  PRODUCT_POLICY_INCLUDES,
  PRODUCT_ASSETS_INCLUDE,
  PRODUCT_FACETS_INCLUDE,
  PRODUCT_CUSTOMIZATIONS_INCLUDE
} from 'constants/product';

/**
 * Optimized Product API Slice for Admin UI
 * Provides granular endpoints to prevent memory overload
 */
export const optimizedProductApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Critical product data (immediate display)
    getProductCritical: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: PRODUCT_CRITICAL_INCLUDES
          })
        }
      })
    }),

    // Basic product info (specifications, details, box contents)
    getProductBasicInfo: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: PRODUCT_BASIC_INFO_INCLUDES
          })
        }
      })
    }),

    // Extended product info (more info, uniqueness, suitability)
    getProductExtendedInfo: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: PRODUCT_EXTENDED_INFO_INCLUDES
          })
        }
      })
    }),

    // Product policies (return policy, terms, disclaimer, personal work)
    getProductPolicies: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: PRODUCT_POLICY_INCLUDES
          })
        }
      })
    }),

    // Product assets (images, videos)
    getProductAssets: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: [PRODUCT_ASSETS_INCLUDE]
          })
        }
      })
    }),

    // Product facets (tags, categories)
    getProductFacets: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: [PRODUCT_FACETS_INCLUDE]
          })
        }
      })
    }),

    // Product customizations
    getProductCustomizations: builder.query<Product, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams({
            ...filter,
            include: [PRODUCT_CUSTOMIZATIONS_INCLUDE]
          })
        }
      })
    })
  })
});

export const {
  useGetProductCriticalQuery,
  useGetProductBasicInfoQuery,
  useGetProductExtendedInfoQuery,
  useGetProductPoliciesQuery,
  useGetProductAssetsQuery,
  useGetProductFacetsQuery,
  useGetProductCustomizationsQuery,
  useLazyGetProductCriticalQuery,
  useLazyGetProductBasicInfoQuery,
  useLazyGetProductExtendedInfoQuery,
  useLazyGetProductPoliciesQuery,
  useLazyGetProductAssetsQuery,
  useLazyGetProductFacetsQuery,
  useLazyGetProductCustomizationsQuery
} = optimizedProductApiSlice;
