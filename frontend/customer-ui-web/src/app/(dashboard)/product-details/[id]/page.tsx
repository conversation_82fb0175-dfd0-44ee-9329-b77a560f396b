'use client';
import ProductAddress from 'views/product-details/productAddress';

import {Box} from '@mui/material';
import {useParams, useRouter} from 'next/navigation';
import {useLazyGetProductCustomizationsQuery} from 'redux/ecom/ecomApiSlice';
import {useOptimizedProductVariant} from 'hooks/useOptimizedProductVariant';
import {fieldsExcludeMetaFields} from 'types/api';
import {useEffect} from 'react';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useAppSelector} from 'redux/hooks';

import ProductPreviewCard from 'views/product-details/ProductPreviewCard';
import {SimilarProductsSection} from 'views/product-details/SimilarProductsSection';
// Constants and types are now used inside the hook

// For development testing: uncomment to analyze optimization
// import { runOptimizationTest } from 'utils/productOptimizationTest';

export default function ProductPreview() {
  // For development: uncomment to test optimization
  // useEffect(() => {
  //   runOptimizationTest();
  // }, []);
  const params = useParams();
  const router = useRouter();
  const productVariantId =
    typeof params.id === 'string' ? params.id : (params.id?.[0] ?? '');

  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {
    data: user,
    isLoading: userLoading,
    isError: userError,
  } = useGetUserQuery(undefined, {
    skip: !isLoggedIn,
  });

  // Use optimized hook for progressive data loading
  const {
    productVariant: combinedProductVariant,
    isLoading,
    isError,
    isSecondaryDataLoading,
  } = useOptimizedProductVariant({
    productVariantId,
    isLoggedIn,
    userId: user?.profileId,
    loadSecondaryData: true,
    loadReviews: true,
  });

  // Data merging is now handled by the useOptimizedProductVariant hook

  const [
    triggerGetCustomizations,
    {data: customizationFields, isFetching, isLoading: customizationLoading},
  ] = useLazyGetProductCustomizationsQuery();

  // Step 1: Fetch variant-specific customizations
  useEffect(() => {
    if (!combinedProductVariant?.id || isFetching || customizationLoading)
      return;
    const fetchCustomizationFields = async () => {
      const res = await triggerGetCustomizations({
        where: {
          productVariantId: combinedProductVariant.id,
        },
        include: [
          {
            relation: 'productCustomizationOptions',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
        ],
      }).unwrap();

      // Step 2: If no variant-specific fields, fallback to productId
      if (!res.length && combinedProductVariant.product?.id) {
        await triggerGetCustomizations({
          where: {
            productId: combinedProductVariant.product.id,
          },
          include: [
            {
              relation: 'productCustomizationOptions',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
          ],
        });
      }
    };

    fetchCustomizationFields();
  }, [combinedProductVariant, triggerGetCustomizations]);

  if (isLoading || userLoading) return <div>Loading...</div>;
  if (isError || userError) return <div>Failed to load product variant.</div>;

  if (!combinedProductVariant) return <div>No product variant found.</div>;

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      {/*
        Optimized loading: Critical data loads first for immediate render,
        secondary data (reviews, detailed specs, etc.) loads progressively
      */}
      {/* Top row: Preview + Address */}
      <Box
        display="flex"
        gap={3}
        flexWrap="wrap"
        justifyContent="space-between"
        alignItems="flex-start"
      >
        <Box flex={1} minWidth={300}>
          <ProductPreviewCard productVariant={combinedProductVariant} />
        </Box>
        <Box flex={1} minWidth={300}>
          <ProductAddress
            productVariant={combinedProductVariant}
            otherVariants={
              combinedProductVariant.product?.productVariants ?? []
            }
            onVariantSelect={id => {
              router.push(`/product-details/${id}`);
            }}
            productCustomizations={customizationFields}
          />
        </Box>
      </Box>
      {/* Bottom row: Similar products */}
      <Box mt={3}>
        <SimilarProductsSection
          productId={combinedProductVariant.product?.id ?? ''}
          isLoggedIn={isLoggedIn}
          user={user}
        />
      </Box>
      {/* Optional: Show loading indicator for secondary data */}
      {isSecondaryDataLoading && (
        <Box
          position="fixed"
          bottom={20}
          right={20}
          bgcolor="rgba(0,0,0,0.7)"
          color="white"
          p={1}
          borderRadius={1}
          fontSize="0.8rem"
        >
          Loading additional details...
        </Box>
      )}
    </Box>
  );
}
