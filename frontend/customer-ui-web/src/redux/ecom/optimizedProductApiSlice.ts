import {ApiSliceIdentifier} from 'types/api';
import {buildFilterParams} from 'utils/buildFilterParams';
import {IFilter} from 'types/filter';
import {ProductVariant, Product} from 'types/product';
import {apiSlice} from 'redux/apiSlice';

/**
 * Optimized API slice for product variant queries
 * Splits large queries into smaller, focused requests to prevent memory issues
 */
export const optimizedProductApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    // Get product variant with only critical relations for immediate render
    getProductVariantCritical: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get product variant secondary data (specs, details, etc.)
    getProductVariantSecondary: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get product variant reviews separately (can be large)
    getProductVariantReviews: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get product with only critical relations
    getProductCritical: builder.query<Product, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get product secondary data
    getProductSecondary: builder.query<Product, {id: string; filter?: IFilter}>(
      {
        query: ({id, filter}) => ({
          url: `/products/${id}`,
          method: 'GET',
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          params: {
            filter: buildFilterParams(filter),
          },
        }),
      },
    ),
  }),
});

export const {
  useGetProductVariantCriticalQuery,
  useLazyGetProductVariantCriticalQuery,
  useGetProductVariantSecondaryQuery,
  useLazyGetProductVariantSecondaryQuery,
  useGetProductVariantReviewsQuery,
  useLazyGetProductVariantReviewsQuery,
  useGetProductCriticalQuery,
  useLazyGetProductCriticalQuery,
  useGetProductSecondaryQuery,
  useLazyGetProductSecondaryQuery,
} = optimizedProductApiSlice;
