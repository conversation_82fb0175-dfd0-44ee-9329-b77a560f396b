import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from '../../redux/apiSlice';
import {Facet} from 'types/auth';
import {Product, ProductCustomField, ProductVariant} from 'types/product';
import {buildFilterParams, IFilter} from 'types/api';

export const ecomApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getFacets: builder.query<Facet[], void>({
      query: () => ({
        url: '/facets',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'facetValues',
              },
            ],
            order: 'name ASC',
          }),
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
    getproducts: builder.query<Product[], void>({
      query: () => ({
        url: '/products',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'featuredAsset',
              },
              {
                relation: 'productCustomizationFields',
              },
            ],
          }),
        },
      }),
    }),

    getProductById: builder.query<Product, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getProductVariantById: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getProductCustomizations: builder.query<ProductCustomField[], IFilter>({
      query: filter => ({
        url: '/product-customization-fields',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Optimized endpoint for secondary product variant data
    getProductVariantSecondaryData: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Optimized endpoint for product reviews
    getProductVariantReviews: builder.query<
      ProductVariant,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/product-variants/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Optimized endpoint for secondary product data
    getProductSecondaryData: builder.query<
      Product,
      {id: string; filter?: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/products/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getProductVariants: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/product-variants',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
  }),
});
export const {
  useLazyGetProductVariantsQuery,
  useGetFacetsQuery,
  useGetproductsQuery,
  useGetProductByIdQuery,
  useGetProductVariantByIdQuery,
  useGetProductCustomizationsQuery,
  useLazyGetProductCustomizationsQuery,
  useGetProductVariantSecondaryDataQuery,
  useLazyGetProductVariantSecondaryDataQuery,
  useGetProductVariantReviewsQuery,
  useLazyGetProductVariantReviewsQuery,
  useGetProductSecondaryDataQuery,
  useLazyGetProductSecondaryDataQuery,
} = ecomApiSlice;
