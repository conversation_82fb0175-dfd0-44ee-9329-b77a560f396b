import { useEffect, useMemo } from 'react';
import { fieldsExcludeMetaFields } from 'types/api';
import { ProductVariant } from 'types/product';
import {
  useGetProductVariantCriticalQuery,
  useGetProductCriticalQuery,
  useLazyGetProductVariantSecondaryQuery,
  useLazyGetProductVariantReviewsQuery,
  useLazyGetProductSecondaryQuery,
} from 'redux/ecom/optimizedProductApiSlice';
import {
  PRODUCT_VARIANT_CRITICAL_RELATIONS,
  PRODUCT_VARIANT_SECONDARY_RELATIONS,
  PRODUCT_VARIANT_REVIEWS_RELATION,
  PRODUCT_CRITICAL_RELATIONS,
  PRODUCT_SECONDARY_RELATIONS,
  getWishlistRelation,
} from 'constants/product-detail';

interface UseOptimizedProductVariantOptions {
  productVariantId: string;
  isLoggedIn?: boolean;
  userId?: string;
  loadSecondaryData?: boolean;
  loadReviews?: boolean;
}

interface UseOptimizedProductVariantReturn {
  productVariant: ProductVariant | undefined;
  isLoading: boolean;
  isError: boolean;
  isSecondaryDataLoading: boolean;
  error: any;
}

/**
 * Optimized hook for loading product variant data progressively
 * Loads critical data first, then secondary data in background
 */
export function useOptimizedProductVariant({
  productVariantId,
  isLoggedIn = false,
  userId,
  loadSecondaryData = true,
  loadReviews = true,
}: UseOptimizedProductVariantOptions): UseOptimizedProductVariantReturn {
  
  // Load critical variant data first
  const {
    data: variantCritical,
    isLoading: variantLoading,
    isError: variantError,
    error: variantErrorData,
  } = useGetProductVariantCriticalQuery({
    id: productVariantId,
    filter: {
      fields: fieldsExcludeMetaFields,
      include: [
        ...PRODUCT_VARIANT_CRITICAL_RELATIONS,
        ...(isLoggedIn && userId ? [getWishlistRelation(userId)] : []),
      ],
    },
  });

  // Load critical product data
  const {
    data: productCritical,
    isLoading: productLoading,
    isError: productError,
    error: productErrorData,
  } = useGetProductCriticalQuery(
    {
      id: variantCritical?.productId || '',
      filter: {
        fields: fieldsExcludeMetaFields,
        include: PRODUCT_CRITICAL_RELATIONS,
      },
    },
    {
      skip: !variantCritical?.productId,
    }
  );

  // Lazy queries for secondary data
  const [
    triggerVariantSecondary,
    { data: variantSecondary, isLoading: variantSecondaryLoading },
  ] = useLazyGetProductVariantSecondaryQuery();

  const [
    triggerVariantReviews,
    { data: variantReviews, isLoading: reviewsLoading },
  ] = useLazyGetProductVariantReviewsQuery();

  const [
    triggerProductSecondary,
    { data: productSecondary, isLoading: productSecondaryLoading },
  ] = useLazyGetProductSecondaryQuery();

  // Load secondary data after critical data is available
  useEffect(() => {
    if (!variantCritical?.id || !loadSecondaryData) return;

    triggerVariantSecondary({
      id: variantCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
        include: PRODUCT_VARIANT_SECONDARY_RELATIONS,
      },
    });
  }, [variantCritical?.id, loadSecondaryData, triggerVariantSecondary]);

  // Load reviews separately
  useEffect(() => {
    if (!variantCritical?.id || !loadReviews) return;

    triggerVariantReviews({
      id: variantCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
        include: PRODUCT_VARIANT_REVIEWS_RELATION,
      },
    });
  }, [variantCritical?.id, loadReviews, triggerVariantReviews]);

  // Load product secondary data
  useEffect(() => {
    if (!productCritical?.id || !loadSecondaryData) return;

    triggerProductSecondary({
      id: productCritical.id,
      filter: {
        fields: fieldsExcludeMetaFields,
        include: PRODUCT_SECONDARY_RELATIONS,
      },
    });
  }, [productCritical?.id, loadSecondaryData, triggerProductSecondary]);

  // Combine all data
  const combinedProductVariant = useMemo(() => {
    if (!variantCritical) return undefined;

    return {
      ...variantCritical,
      // Merge secondary variant data
      ...(variantSecondary && {
        productSpecifications: variantSecondary.productSpecifications || variantCritical.productSpecifications,
        productBoxContents: variantSecondary.productBoxContents || variantCritical.productBoxContents,
        productDetail: variantSecondary.productDetail || variantCritical.productDetail,
        productMoreInfo: variantSecondary.productMoreInfo || variantCritical.productMoreInfo,
        productUniqueness: variantSecondary.productUniqueness || variantCritical.productUniqueness,
        productSuitability: variantSecondary.productSuitability || variantCritical.productSuitability,
        productReturnPolicy: variantSecondary.productReturnPolicy || variantCritical.productReturnPolicy,
        productTermsAndCondition: variantSecondary.productTermsAndCondition || variantCritical.productTermsAndCondition,
        productDisclaimer: variantSecondary.productDisclaimer || variantCritical.productDisclaimer,
        productPersonalWork: variantSecondary.productPersonalWork || variantCritical.productPersonalWork,
      }),
      // Merge reviews
      ...(variantReviews && {
        reviews: variantReviews.reviews || variantCritical.reviews,
      }),
      // Merge product data
      product: productCritical && {
        ...productCritical,
        ...(productSecondary && {
          productDetail: productSecondary.productDetail || productCritical.productDetail,
          productReturnPolicy: productSecondary.productReturnPolicy || productCritical.productReturnPolicy,
          productMoreInfo: productSecondary.productMoreInfo || productCritical.productMoreInfo,
          productTermsAndCondition: productSecondary.productTermsAndCondition || productCritical.productTermsAndCondition,
          productSpecifications: productSecondary.productSpecifications || productCritical.productSpecifications,
          productBoxContents: productSecondary.productBoxContents || productCritical.productBoxContents,
          productUniqueness: productSecondary.productUniqueness || productCritical.productUniqueness,
          productSuitability: productSecondary.productSuitability || productCritical.productSuitability,
          productDisclaimer: productSecondary.productDisclaimer || productCritical.productDisclaimer,
          productCustomizationFields: productSecondary.productCustomizationFields || productCritical.productCustomizationFields,
          productPersonalWork: productSecondary.productPersonalWork || productCritical.productPersonalWork,
        }),
      },
    } as ProductVariant;
  }, [variantCritical, productCritical, variantSecondary, variantReviews, productSecondary]);

  return {
    productVariant: combinedProductVariant,
    isLoading: variantLoading || productLoading,
    isError: variantError || productError,
    isSecondaryDataLoading: variantSecondaryLoading || reviewsLoading || productSecondaryLoading,
    error: variantErrorData || productErrorData,
  };
}
