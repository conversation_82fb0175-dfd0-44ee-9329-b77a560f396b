import {fieldsExcludeMetaFields} from 'types/api';
import {ReviewStatus} from 'types/review';

// Critical relations needed for initial render (above the fold content)
export const PRODUCT_VARIANT_CRITICAL_RELATIONS = [
  {
    relation: 'productVariantPrice',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productVariantOptions',
    scope: {
      fields: fieldsExcludeMetaFields,
      include: [
        {
          relation: 'productOption',
          scope: {
            fields: fieldsExcludeMetaFields,
            include: [
              {
                relation: 'productOptionGroup',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
            ],
          },
        },
      ],
    },
  },
  {
    relation: 'productVariantAssets',
    scope: {
      fields: fieldsExcludeMetaFields,
      include: [
        {
          relation: 'asset',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
      ],
    },
  },
  {
    relation: 'featuredAsset',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
];

// Secondary relations for product details (loaded separately)
export const PRODUCT_VARIANT_SECONDARY_RELATIONS = [
  {
    relation: 'productSpecifications',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productBoxContents',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productDetail',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productMoreInfo',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productUniqueness',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productSuitability',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productReturnPolicy',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productTermsAndCondition',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productDisclaimer',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productPersonalWork',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
];

// Reviews relation (loaded separately due to potential size)
export const PRODUCT_VARIANT_REVIEWS_RELATION = [
  {
    relation: 'reviews',
    scope: {
      where: {
        deleted: false,
        status: ReviewStatus.APPROVED,
      },
      include: [
        {
          relation: 'customer',
        },
      ],
    },
  },
];

// Legacy export for backward compatibility (now uses critical relations only)
export const PRODUCT_VARIANT_RELATIONS = PRODUCT_VARIANT_CRITICAL_RELATIONS;

// Critical product relations needed immediately
export const PRODUCT_CRITICAL_RELATIONS = [
  {
    relation: 'seller',
    scope: {
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [
              {
                relation: 'user',
              },
            ],
          },
        },
      ],
    },
  },
  {
    relation: 'productVariants',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
];

// Secondary product relations (loaded separately)
export const PRODUCT_SECONDARY_RELATIONS = [
  {
    relation: 'productDetail',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productReturnPolicy',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productMoreInfo',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productTermsAndCondition',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productSpecifications',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productBoxContents',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productUniqueness',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productSuitability',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productDisclaimer',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productCustomizationFields',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
  {
    relation: 'productPersonalWork',
    scope: {
      fields: fieldsExcludeMetaFields,
    },
  },
];

// Legacy export for backward compatibility (now uses critical relations only)
export const PRODUCT_RELATIONS = PRODUCT_CRITICAL_RELATIONS;

// Helper function to get wishlist relation for logged-in users
export const getWishlistRelation = (customerId?: string) => ({
  relation: 'wishlist',
  scope: {
    where: {
      deleted: false,
      customerId,
    },
    fields: {
      id: true,
    },
  },
});
