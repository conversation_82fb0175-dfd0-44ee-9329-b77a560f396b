/**
 * Utility to test and compare the memory usage of optimized vs original product queries
 * This can be used in development to verify the optimization is working
 */

import {
  PRODUCT_VARIANT_CRITICAL_RELATIONS,
  PRODUCT_VARIANT_SECONDARY_RELATIONS,
  PRODUCT_VARIANT_REVIEWS_RELATION,
  PRODUCT_CRITICAL_RELATIONS,
  PRODUCT_SECONDARY_RELATIONS,
} from 'constants/product-detail';

/**
 * Calculate the approximate size of a query based on number of relations
 * This is a rough estimate for comparison purposes
 */
function calculateQueryComplexity(relations: any[]): number {
  let complexity = 0;
  
  relations.forEach(relation => {
    complexity += 1; // Base relation
    if (relation.scope?.include) {
      complexity += calculateQueryComplexity(relation.scope.include);
    }
  });
  
  return complexity;
}

/**
 * Compare the complexity of optimized vs original approach
 */
export function compareQueryOptimization() {
  // Original approach (all relations in one query)
  const originalVariantComplexity = calculateQueryComplexity([
    ...PRODUCT_VARIANT_CRITICAL_RELATIONS,
    ...PRODUCT_VARIANT_SECONDARY_RELATIONS,
    ...PRODUCT_VARIANT_REVIEWS_RELATION,
  ]);
  
  const originalProductComplexity = calculateQueryComplexity([
    ...PRODUCT_CRITICAL_RELATIONS,
    ...PRODUCT_SECONDARY_RELATIONS,
  ]);
  
  const originalTotalComplexity = originalVariantComplexity + originalProductComplexity;
  
  // Optimized approach (split into multiple queries)
  const optimizedCriticalVariantComplexity = calculateQueryComplexity(PRODUCT_VARIANT_CRITICAL_RELATIONS);
  const optimizedCriticalProductComplexity = calculateQueryComplexity(PRODUCT_CRITICAL_RELATIONS);
  const optimizedSecondaryVariantComplexity = calculateQueryComplexity(PRODUCT_VARIANT_SECONDARY_RELATIONS);
  const optimizedSecondaryProductComplexity = calculateQueryComplexity(PRODUCT_SECONDARY_RELATIONS);
  const optimizedReviewsComplexity = calculateQueryComplexity(PRODUCT_VARIANT_REVIEWS_RELATION);
  
  // Critical data (loaded first)
  const optimizedCriticalComplexity = optimizedCriticalVariantComplexity + optimizedCriticalProductComplexity;
  
  // Secondary data (loaded progressively)
  const optimizedSecondaryComplexity = optimizedSecondaryVariantComplexity + optimizedSecondaryProductComplexity + optimizedReviewsComplexity;
  
  const results = {
    original: {
      totalComplexity: originalTotalComplexity,
      singleQueryLoad: originalTotalComplexity,
    },
    optimized: {
      criticalComplexity: optimizedCriticalComplexity,
      secondaryComplexity: optimizedSecondaryComplexity,
      totalComplexity: optimizedCriticalComplexity + optimizedSecondaryComplexity,
      initialLoadReduction: ((originalTotalComplexity - optimizedCriticalComplexity) / originalTotalComplexity * 100).toFixed(1),
    },
  };
  
  console.log('🚀 Product Query Optimization Analysis:');
  console.log('=====================================');
  console.log(`Original approach: ${results.original.totalComplexity} relations in single query`);
  console.log(`Optimized critical: ${results.optimized.criticalComplexity} relations (initial load)`);
  console.log(`Optimized secondary: ${results.optimized.secondaryComplexity} relations (background load)`);
  console.log(`Initial load reduction: ${results.optimized.initialLoadReduction}%`);
  console.log('=====================================');
  
  return results;
}

/**
 * Monitor memory usage during product loading (for development)
 */
export function monitorMemoryUsage(label: string) {
  if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
    const memory = (window.performance as any).memory;
    console.log(`📊 Memory Usage - ${label}:`, {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
    });
  }
}

/**
 * Test function to be called in development
 */
export function runOptimizationTest() {
  console.log('🧪 Running Product Optimization Test...');
  monitorMemoryUsage('Before optimization test');
  const results = compareQueryOptimization();
  monitorMemoryUsage('After optimization test');
  return results;
}
